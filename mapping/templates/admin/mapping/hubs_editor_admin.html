{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}Групповое редактирование хабов | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; Групповое редактирование хабов
</div>
{% endblock %}

{% block nav-sidebar %}
{% include "admin/nav_sidebar.html" %}
{% endblock %}

{% block extrahead %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static 'map/css/leaflet.css' %}">
<link rel="stylesheet" type="text/css" href="{% static 'map/css/Control.Geocoder.css' %}"/>
<link rel="stylesheet" href="{% static 'map/css/leaflet.draw.css' %}">



<!-- Подключаем jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Подключаем Bootstrap для модальных окон -->
<link rel="stylesheet" href="{% static 'map/css/bootstrap.min.css' %}">
<script src="{% static 'map/js/bootstrap.min.js' %}"></script>

<!-- Подключаем JS файлы -->
<script src="{% static 'map/js/leaflet.js' %}"></script>
<script src="{% static 'map/js/leaflet.draw.js' %}"></script>
<script src="{% static 'map/js/turf.min.js' %}"></script>
<script src="{% static 'map/js/Control.Geocoder.js' %}"></script>

<script>
$(document).ready(function () {
    // Дефолтные координаты для Казахстана (Алматы)
    var defaultLat = 43.257109;
    var defaultLon = 76.946314;

    // Проверяем есть ли валидные координаты региона
    var regionLat = {% if regions.0.lat %}{{ regions.0.lat }}{% else %}null{% endif %};
    var regionLon = {% if regions.0.lon %}{{ regions.0.lon }}{% else %}null{% endif %};

    // Используем координаты региона или дефолтные
    var mapLat = (regionLat !== null && !isNaN(regionLat)) ? regionLat : defaultLat;
    var mapLon = (regionLon !== null && !isNaN(regionLon)) ? regionLon : defaultLon;

    // Создаем Canvas рендерер для лучшей производительности с большим количеством маркеров
    var canvasRenderer = L.canvas();

    var map = L.map('map', {
        renderer: canvasRenderer,
        preferCanvas: true
    }).setView([mapLat, mapLon], 12);

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '<a href="https://www.kazpost.kz/kk">KAZPOST</a> contributors'
    }).addTo(map);

    var departments = null;
    var selectedPolygon = null;
    var outsidePoints = []; // Точки вне полигонов хабов из базы данных

    // Загрузка отделений для региона
    function loadDepartmentsForRegion(selectedRegion, callback) {
        $.getJSON('{{ KAZPOSTGEO_PREFIX }}/get_departments/?region_id=' + selectedRegion, function (data) {
            if (data.length > 0) {
                var firstDepartment = data[0];
                console.log('firstDepartment: ', firstDepartment)
                var lat = firstDepartment.lat;
                var lon = firstDepartment.lon;
                console.log('lat: ', firstDepartment.lat)
            }
            departments = data;
            callback();
        });
    }

    // Функция для отображения отделений на карте
    function displayDepartments() {
        departments.forEach(function (point) {
            var lat = point.lat;
            var lon = point.lon;
            var name = point.name;

            // Проверяем валидность координат перед созданием маркера
            if (lat && lon && !isNaN(lat) && !isNaN(lon)) {
                // Создание маркера для точки
                var marker = L.marker([lat, lon]);

                // Добавление всплывающей подсказки с именем точки
                marker.bindPopup(name);

                marker.addTo(map);
            } else {
                console.warn('Невалидные координаты для отделения:', name, lat, lon);
            }
        });
    }

    var drawnItems = new L.FeatureGroup();
    map.addLayer(drawnItems);

    var drawControl = new L.Control.Draw({
        draw: {
            polygon: true,
            polyline: false,
            rectangle: false,
            circle: false,
            marker: false,
        },
        edit: {
            featureGroup: drawnItems,
            edit: true,
        },
    });
    map.addControl(drawControl);
    map.attributionControl.setPrefix('Kazpost Digital, 2024');

    // Обработчики селектов
    function updateButtonState() {
        var layerId = $('#layer-select').val();
        var regionId = $('#region-select').val();
        $('#load-hubs-button').prop('disabled', !layerId || !regionId);
    }

    $('#layer-select, #region-select').on('change', updateButtonState);
    updateButtonState(); // Инициализация

    // Обработчик кнопки загрузки хабов
    $('#load-hubs-button').on('click', function() {
        var selectedLayer = $('#layer-select').val();
        var selectedRegion = $('#region-select').val();
        var showPolygonsOnly = $('#show-polygons').is(':checked');

        if (!selectedLayer || !selectedRegion) {
            alert('Выберите слой и регион');
            return;
        }

        // Очистка карты от предыдущих полигонов и маркеров
        map.eachLayer(function (layer) {
            if (layer instanceof L.Polygon || layer instanceof L.Marker) {
                map.removeLayer(layer);
            }
        });

        // ВАЖНО: Очищаем красные маркеры сразу при загрузке новых полигонов
        redMarkersGroup.clearLayers();
        outsidePoints = []; // Сбрасываем кэш точек, чтобы загрузить новые для текущего слоя
        $('#show-red-markers').prop('checked', false); // Сбрасываем чекбокс
        console.log('🧹 Красные маркеры очищены при загрузке полигонов');

        // Загрузка данных об отделениях и отображение их на карте
        if (!showPolygonsOnly){
            loadDepartmentsForRegion(selectedRegion, displayDepartments);
        }

        // Загрузка и отображение полигонов, связанных с выбранным слоем
        var url = '{{ KAZPOSTGEO_PREFIX }}/get_layer_polygons/?layer_id=' + selectedLayer + '&region_id=' + selectedRegion;

        $.getJSON(url, function (data) {
            console.log("=== ЗАГРУЖЕНЫ ПОЛИГОНЫ ===");
            console.log("Всего полигонов:", data.polygons.length);

            // Сбрасываем отслеживание изменений при загрузке новых данных
            changedPolygons.clear();
            updateStatus();

            // Логируем все полигоны как раскрываемые объекты ДО редактирования
            var originalData = data.polygons.map(function(polygon) {
                return {
                    id: polygon.id,
                    department: polygon.departament,
                    status: polygon.status,
                    coordinates: polygon.geometry.coordinates[0].map(function(coord) {
                        return [parseFloat(coord[1].toFixed(6)), parseFloat(coord[0].toFixed(6))]; // [lat, lon]
                    })
                };
            });
            console.log("=== ИСХОДНЫЕ ДАННЫЕ ===");
            console.log(originalData);
            console.log("=== КОНЕЦ ИСХОДНЫХ ДАННЫХ ===");

            data.polygons.forEach(function (polygon) {
                var coords = polygon.geometry.coordinates[0];
                var latLngs = coords.map(function (coord) {
                    return [coord[1], coord[0]];
                });

                var fillColor = 'blue'; // Цвет по умолчанию
                // Изменение цвета в зависимости от статуса
                if (polygon.status !== 1) {
                    fillColor = 'red'; // Или любой другой цвет для статуса, отличного от 1
                }

                var polygonLayer = L.polygon(latLngs, {fillColor: fillColor,
                    interactive: true,
                });

                // Добавляем ID хаба и название отделения к полигону для отслеживания
                polygonLayer.hubId = polygon.id;
                polygonLayer.departmentName = polygon.departament;

                polygonLayer.bindTooltip(polygon.departament + ' (ID: ' + polygon.id + ')', {
                    permanent: false,
                    direction: 'auto',
                });  // Всплывающая подсказка

                polygonLayer.on('mouseover', function () {
                    if (selectedPolygon !== polygonLayer) {
                        polygonLayer.setStyle({fillColor: 'green'});  // Изменение цвета при наведении
                    }
                });

                polygonLayer.on('mouseout', function () {
                    if (selectedPolygon !== polygonLayer) {
                        if (polygon.status !== 1) {
                            polygonLayer.setStyle({fillColor: 'red'}); // Или любой другой цвет для статуса, отличного от 1
                        } else {
                            polygonLayer.setStyle({fillColor: 'blue'});
                        }
                        // Возвращение исходного цвета
                    }
                });

                drawnItems.addLayer(polygonLayer);  // Добавляем в группу редактируемых полигонов

                polygonLayer.addTo(map);

                // Добавление маркера для координат департамента
                var departmentMarker = L.marker([polygon.lat, polygon.lon]).addTo(map);
                departmentMarker.bindPopup(polygon.departament).openPopup();
            });
            console.log("region_lat ",data.region_lat)

            // Проверяем валидность координат региона перед установкой вида карты
            if (data.region_lat && data.region_lon &&
                !isNaN(data.region_lat) && !isNaN(data.region_lon)) {
                map.setView([data.region_lat, data.region_lon], 12);
                console.log("Карта перемещена к региону:", data.region_lat, data.region_lon);
            } else {
                console.warn("Невалидные координаты региона, карта остается на текущей позиции");
            }

            // Загружаем точки вне полигонов хабов
            loadOutsidePoints();
        });
    });

    var changedPolygons = new Map(); // Отслеживаем измененные полигоны: ID -> название
    var redMarkersGroup = L.layerGroup(); // Группа для красных маркеров

    // Функция загрузки точек вне полигонов хабов
    function loadOutsidePoints() {
        console.log('🔄 Начинаем загрузку точек вне полигонов...');

        // Получаем текущие значения фильтров
        var selectedLayer = $('#layer-select').val();
        var selectedRegion = $('#region-select').val();

        console.log('🔍 ОТЛАДКА ПАРАМЕТРОВ:');
        console.log('   selectedLayer:', selectedLayer);
        console.log('   selectedRegion:', selectedRegion);
        console.log('   KAZPOSTGEO_PREFIX:', '{{ KAZPOSTGEO_PREFIX }}');

        var url = '{{ KAZPOSTGEO_PREFIX }}/get_points_outside_hubs/';
        var params = [];

        // Добавляем параметры фильтрации
        if (selectedRegion) {
            params.push('region_id=' + selectedRegion);
            console.log('📍 Фильтр по региону:', selectedRegion);
        }
        if (selectedLayer) {
            params.push('layer_id=' + selectedLayer);
            console.log('🗂️ Фильтр по слою:', selectedLayer);
        }

        if (params.length > 0) {
            url += '?' + params.join('&');
        }

        console.log('🔗 URL запроса:', url);
        console.log('🚀 ОТПРАВЛЯЕМ AJAX ЗАПРОС...');

        $.ajax({
            url: url,
            type: 'GET',
            // Убираем ограничение таймаута для загрузки точек
            beforeSend: function() {
                console.log('📡 AJAX запрос отправлен на:', url);
            },
            success: function(data) {
                console.log('📥 ПОЛУЧЕН ОТВЕТ ОТ СЕРВЕРА:', data);
                if (data.success) {
                    outsidePoints = data.points;
                    console.log('✅ Загружено точек вне полигонов:', data.total_count);
                    console.log('Точки вне полигонов:', outsidePoints);

                    // Автоматически показываем маркеры
                    addRedMarkers();
                } else {
                    console.error('❌ Ошибка загрузки точек:', data.error);
                    outsidePoints = [];
                }
            },
            error: function(xhr, status, error) {
                console.error('❌ AJAX ошибка при загрузке точек:', error);
                console.error('Status:', status);
                console.error('Response:', xhr.responseText);
                console.error('Response Status:', xhr.status);
                outsidePoints = [];
            }
        });
    }


    // Функция добавления красных маркеров
    function addRedMarkers() {
        // Очищаем предыдущие маркеры
        redMarkersGroup.clearLayers();

        if (outsidePoints.length === 0) {
            console.log('Нет точек для отображения');
            return;
        }

        console.log('=== ОТЛАДКА КРАСНЫХ МАРКЕРОВ ===');
        console.log('Всего точек для отображения:', outsidePoints.length);
        console.log('Первые 3 точки:', outsidePoints.slice(0, 3));

        var validPoints = 0;
        var invalidPoints = 0;

        // Создаем красные маркеры
        outsidePoints.forEach(function(point, index) {
            // Проверяем валидность координат
            if (!point.lat || !point.lon || isNaN(point.lat) || isNaN(point.lon)) {
                invalidPoints++;
                console.warn(`Точка ${index} имеет невалидные координаты:`, point);
                return;
            }

            validPoints++;

            try {
                // Используем CircleMarker вместо divIcon для ЗНАЧИТЕЛЬНО лучшей производительности
                // CircleMarker рендерится как SVG/Canvas, а не как DOM элементы
                var marker = L.circleMarker([point.lat, point.lon], {
                    radius: 6,
                    fillColor: 'red',
                    color: 'white',
                    weight: 2,
                    opacity: 1,
                    fillOpacity: 0.8,
                    renderer: canvasRenderer  // Принудительно используем Canvas
                }).bindPopup(`<b>ID:</b> ${point.id}<br><b>Координаты:</b> ${point.lat.toFixed(6)}, ${point.lon.toFixed(6)}<br><b>Статус:</b> Вне полигонов хабов`);

                redMarkersGroup.addLayer(marker);

                // Логируем первые 3 маркера
                if (index < 3) {
                    console.log(`Маркер ${index + 1} создан: lat=${point.lat}, lon=${point.lon}`);
                }
            } catch (error) {
                console.error(`Ошибка создания маркера ${index}:`, error, point);
            }
        });

        // Добавляем группу на карту
        redMarkersGroup.addTo(map);

        console.log(`Создано валидных маркеров: ${validPoints}`);
        console.log(`Невалидных точек: ${invalidPoints}`);
        console.log('Группа маркеров добавлена на карту');
        console.log('=== КОНЕЦ ОТЛАДКИ МАРКЕРОВ ===');
    }

    // Функция обновления статуса
    function updateStatus() {
        var totalChanges = changedPolygons.size;
        var statusText = '';

        if (totalChanges > 0) {
            statusText = '<strong>Статус:</strong> <span style="color: orange;">Есть несохраненные изменения</span><br>';

            if (changedPolygons.size > 0) {
                var changedNames = Array.from(changedPolygons.values()).join(', ');
                statusText += '• Отредактировано: ' + changedNames + '<br>';
            }



            $('#save-hubs-button, #reset-hubs-button').prop('disabled', false);
        } else {
            statusText = '<strong>Статус:</strong> Нет изменений';
            $('#save-hubs-button, #reset-hubs-button').prop('disabled', true);
        }

        $('#hubs-status').html(statusText);
    }

    // Обработчики редактирования полигонов
    map.on(L.Draw.Event.EDITED, function (event) {
        var layers = event.layers;

        layers.eachLayer(function (layer) {
            if (layer.hubId && layer.departmentName) {
                // Получаем координаты всех точек полигона ПОСЛЕ редактирования
                var coordinates = layer.toGeoJSON().geometry.coordinates[0];

                var editedData = {
                    id: layer.hubId,
                    department: layer.departmentName,
                    coordinates: coordinates.map(function(coord) {
                        return [parseFloat(coord[1].toFixed(6)), parseFloat(coord[0].toFixed(6))]; // [lat, lon]
                    })
                };

                console.log("=== ПОЛИГОН ОТРЕДАКТИРОВАН ===");
                console.log(editedData);
                console.log("=== КОНЕЦ ДАННЫХ ОТРЕДАКТИРОВАННОГО ПОЛИГОНА ===");

                changedPolygons.set(layer.hubId, layer.departmentName);
                // Меняем цвет отредактированного полигона
                layer.setStyle({fillColor: 'orange', fillOpacity: 0.5});
            }
        });

        updateStatus();
    });

    // Обработчик удаления полигонов
    map.on(L.Draw.Event.DELETED, function (event) {
        var layers = event.layers;

        layers.eachLayer(function (layer) {
            if (layer.hubId && layer.departmentName) {
                // Просто убираем из отслеживания изменений
                changedPolygons.delete(layer.hubId);
                console.log('Полигон убран с карты (НЕ удален из базы):', layer.departmentName);
            }
        });

        updateStatus();
    });

    // Обработчик кнопки "Сохранить изменения"
    $('#save-hubs-button').on('click', function() {
        var totalChanges = changedPolygons.size;

        if (totalChanges === 0) {
            alert('Нет изменений для сохранения');
            return;
        }

        var confirmMessage = 'Сохранить изменения?\n\n';
        if (changedPolygons.size > 0) {
            var changedNames = Array.from(changedPolygons.values()).join(', ');
            confirmMessage += '• Отредактированные: ' + changedNames + '\n';
        }

        confirmMessage += '\n⚠️ ВНИМАНИЕ: Выберите режим сохранения:\n';
        confirmMessage += '• OK = ТЕСТОВЫЙ режим (безопасно)\n';
        confirmMessage += '• Отмена = выход без сохранения';

        if (confirm(confirmMessage)) {
            // Сначала тестовый режим
            saveHubsToDatabase(true);
        }
    });

    // Функция сохранения в базу данных
    function saveHubsToDatabase(testMode) {
        var changedHubsData = [];

        // Собираем данные измененных полигонов
        drawnItems.eachLayer(function(layer) {
            if (layer instanceof L.Polygon && layer.hubId && changedPolygons.has(layer.hubId)) {
                var geometry = layer.toGeoJSON().geometry;

                changedHubsData.push({
                    id: layer.hubId,
                    department: layer.departmentName,
                    geometry: geometry,
                    coordinates: geometry.coordinates[0].map(function(coord) {
                        return [parseFloat(coord[1].toFixed(6)), parseFloat(coord[0].toFixed(6))]; // [lat, lon]
                    })
                });
            }
        });

        console.log("=== ДАННЫЕ ДЛЯ СОХРАНЕНИЯ ===");
        console.log(changedHubsData);
        console.log("=== КОНЕЦ ДАННЫХ ДЛЯ СОХРАНЕНИЯ ===");

        var requestData = {
            changed_hubs: changedHubsData,
            test_mode: testMode
        };

        $.ajax({
            url: '{{ KAZPOSTGEO_PREFIX }}/bulk_save_hubs/',
            type: 'POST',
            data: JSON.stringify(requestData),
            contentType: 'application/json; charset=utf-8',
            headers: {
                'X-CSRFToken': $('[name="csrfmiddlewaretoken"]').val()
            },
            success: function (response) {
                console.log('Ответ сервера:', response);

                if (response.success) {
                    var message = response.message + '\n\n';
                    message += 'Обработано:\n';
                    message += '• Обновлено полигонов: ' + response.processed.updated + '\n';

                    if (response.processed.errors.length > 0) {
                        message += '• Ошибки: ' + response.processed.errors.length + '\n';
                        console.error('Ошибки:', response.processed.errors);
                    }

                    if (testMode) {
                        message += '\n🔄 Выполнить РЕАЛЬНОЕ сохранение?';
                        if (confirm(message)) {
                            saveHubsToDatabase(false); // Реальное сохранение
                        }
                    } else {
                        message += '\n✅ Изменения сохранены в базу данных!';
                        alert(message);

                        // Сбрасываем отслеживание изменений
                        changedPolygons.clear();
                        updateStatus();
                    }
                } else {
                    alert('Ошибка: ' + (response.error || 'Неизвестная ошибка'));
                }
            },
            error: function (xhr, status, error) {
                console.error('AJAX Error:', error);
                console.error('Response:', xhr.responseText);
                alert('Ошибка сохранения: ' + error + '\nПроверьте консоль для деталей');
            }
        });
    }

    // Обработчик кнопки "Отменить изменения"
    $('#reset-hubs-button').on('click', function() {
        var totalChanges = changedPolygons.size;

        if (totalChanges === 0) {
            return;
        }

        var resetMessage = 'Отменить все несохраненные изменения?\n\n';
        if (changedPolygons.size > 0) {
            var changedNames = Array.from(changedPolygons.values()).join(', ');
            resetMessage += '• Отредактированные: ' + changedNames;
        }

        if (confirm(resetMessage)) {
            // Сбрасываем отслеживание изменений
            changedPolygons.clear();

            // Перезагружаем хабы
            $('#load-hubs-button').click();
        }
    });

    // Обработчик чекбокса красных маркеров
    $('#show-red-markers').on('change', function() {
        if ($(this).is(':checked')) {
            if (outsidePoints.length > 0) {
                addRedMarkers();
            } else {
                loadOutsidePoints(); // Загружаем точки если их еще нет
            }
        } else {
            redMarkersGroup.clearLayers();
        }
    });
});
</script>

<style>
    /* Переопределяем стили админки для карты */
    #content {
        margin-left: 0 !important;
        padding: 20px !important;
    }

    /* Принудительно переопределяем стили админки для селектов */
    .hubs-editor-container select {
        background: white !important;
        color: #333 !important;
        border: 1px solid #ccc !important;
        font-size: 14px !important;
        padding: 8px 12px !important;
        height: auto !important;
        line-height: normal !important;
        font-weight: normal !important;
    }

    .hubs-editor-container select option {
        color: #333 !important;
        background: white !important;
    }

    /* Исправляем стили для кнопок */
    .hubs-editor-container button {
        font-size: 14px !important;
        font-weight: normal !important;
        text-transform: none !important;
    }

    /* Контейнер для карты */
    .hubs-editor-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    /* Панель управления */
    .controls-panel {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 20px;
    }

    .controls-row {
        display: flex;
        gap: 20px;
        align-items: end;
        flex-wrap: wrap;
        margin-bottom: 15px;
    }

    .control-group {
        flex: 1;
        min-width: 200px;
    }

    .control-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #495057;
        font-size: 14px;
    }

    .control-group select {
        width: 100%;
        min-width: 200px;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        background-color: white;
        color: #495057 !important;
        font-weight: normal !important;
        height: auto !important;
        line-height: 1.5;
    }

    .control-group select:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }

    .control-group select option {
        color: #495057 !important;
        background-color: white !important;
        padding: 8px;
    }

    /* Кнопки */
    .btn-custom {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        margin-right: 10px;
        transition: all 0.2s;
    }

    .btn-primary-custom {
        background-color: #007bff;
        color: white;
    }

    .btn-primary-custom:hover:not(:disabled) {
        background-color: #0056b3;
    }

    .btn-success-custom {
        background-color: #28a745;
        color: white;
    }

    .btn-success-custom:hover:not(:disabled) {
        background-color: #1e7e34;
    }

    .btn-warning-custom {
        background-color: #ffc107;
        color: #212529;
    }

    .btn-warning-custom:hover:not(:disabled) {
        background-color: #e0a800;
    }

    .btn-custom:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* Чекбокс */
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 10px;
    }

    .checkbox-group input[type="checkbox"] {
        width: auto !important;
        margin: 0 !important;
        height: auto !important;
    }

    .checkbox-group label {
        margin: 0 !important;
        font-weight: normal !important;
        cursor: pointer !important;
        color: #333 !important;
        font-size: 14px !important;
    }

    /* Карта */
    .map-container {
        position: relative;
        height: 600px !important;
        background: #f8f9fa;
        border: 1px solid #ddd;
    }

    #map {
        height: 600px !important;
        width: 100% !important;
        border: none;
        position: relative !important;
        z-index: 1;
    }

    /* Принудительно показываем карту */
    .leaflet-container {
        height: 600px !important;
        width: 100% !important;
    }

    /* Статус */
    .status-panel {
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
        padding: 15px 20px;
        font-size: 14px;
    }

    /* Инструкции */
    .instructions {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 4px;
        padding: 15px;
        margin-top: 15px;
    }

    .instructions h4 {
        margin: 0 0 10px 0;
        color: #0056b3;
        font-size: 14px;
        font-weight: 600;
    }

    .instructions ul {
        margin: 0;
        padding-left: 20px;
    }

    .instructions li {
        margin-bottom: 5px;
        font-size: 13px;
        color: #495057;
    }

    /* Исправляем конфликты с админкой */
    .hubs-editor-container * {
        box-sizing: border-box;
    }

    /* Leaflet стили */
    .leaflet-draw-toolbar {
        display: block !important;
        visibility: visible !important;
    }

    .leaflet-draw {
        display: block !important;
    }

    .leaflet-control-draw {
        display: block !important;
    }

    .leaflet-top.leaflet-left {
        display: block !important;
    }

    /* Адаптивность */
    @media (max-width: 768px) {
        .controls-row {
            flex-direction: column;
        }

        .control-group {
            min-width: auto;
        }

        .map-container {
            height: 400px;
        }

        .btn-custom {
            margin-bottom: 10px;
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="hubs-editor-container">

    {% csrf_token %}

    <!-- Панель управления -->
    <div class="controls-panel">
        <div class="controls-row">
            <div class="control-group">
                <label for="layer-select">Слой:</label>
                <select id="layer-select">
                    <option value="">Выберите слой</option>
                    {% for layer in layers %}
                    <option value="{{ layer.id }}">{{ layer.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="control-group">
                <label for="region-select">Регион:</label>
                <select id="region-select">
                    <option value="">Выберите регион</option>
                    {% for region in regions %}
                    <option value="{{ region.id }}">{{ region.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="control-group">
                <button id="load-hubs-button" type="button" class="btn-custom btn-primary-custom" disabled>Показать полигоны</button>
            </div>
        </div>

        <div class="checkbox-group">
            <input type="checkbox" id="show-polygons" name="show-polygons">
            <label for="show-polygons">Показывать только полигоны</label>
        </div>

        <div class="checkbox-group">
            <input type="checkbox" id="show-red-markers" name="show-red-markers" checked>
            <label for="show-red-markers">Показывать красные маркеры</label>
        </div>

        <div class="controls-row" style="margin-top: 15px;">
            <div class="control-group">
                <button id="save-hubs-button" type="button" class="btn-custom btn-success-custom" disabled>Сохранить изменения</button>
                <button id="reset-hubs-button" type="button" class="btn-custom btn-warning-custom" disabled>Отменить изменения</button>
            </div>
        </div>


    </div>

    <!-- Карта -->
    <div class="map-container">
        <div id="map"></div>
    </div>

    <!-- Статус -->
    <div class="status-panel">
        <div id="hubs-status"><strong>Статус:</strong> Выберите параметры и загрузите хабы</div>
    </div>
</div>
{% endblock %}


