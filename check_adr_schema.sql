-- Исследование схемы adr и связей с адресными данными

-- 1. Проверяем структуру таблицы adr.geo_build
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'adr' 
  AND table_name = 'geo_build'
ORDER BY ordinal_position;

-- 2. Ищем все таблицы в схеме adr
SELECT table_name, table_type
FROM information_schema.tables 
WHERE table_schema = 'adr'
ORDER BY table_name;

-- 3. Ищем таблицы с полем build_id
SELECT 
    table_schema,
    table_name,
    column_name
FROM information_schema.columns 
WHERE column_name LIKE '%build%'
  AND table_schema IN ('adr', 'public')
ORDER BY table_schema, table_name;

-- 4. Ищем адресные таблицы (содержащие address, addr, street)
SELECT 
    table_schema,
    table_name
FROM information_schema.tables 
WHERE table_schema = 'adr'
  AND (table_name LIKE '%address%' 
       OR table_name LIKE '%addr%' 
       OR table_name LIKE '%street%'
       OR table_name LIKE '%building%'
       OR table_name LIKE '%house%')
ORDER BY table_name;

-- 5. Проверяем внешние ключи для adr.geo_build
SELECT
    tc.table_schema, 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_schema AS foreign_table_schema,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_schema = 'adr'
  AND tc.table_name = 'geo_build';
